package main

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"
)

func main() {
	// Create a new application
	myApp := app.New()
	myApp.SetIcon(nil)

	// Create a new window
	myWindow := myApp.NewWindow("Simple Go Window")
	myWindow.Resize(fyne.NewSize(400, 300))

	// Create some content
	hello := widget.NewLabel("Hello, World!")
	hello.Alignment = fyne.TextAlignCenter

	button := widget.NewButton("Click Me!", func() {
		hello.SetText("Button was clicked!")
	})

	// Create a container to hold our widgets
	content := container.NewVBox(
		hello,
		widget.NewSeparator(),
		button,
	)

	// Set the content and show the window
	myWindow.SetContent(content)
	myWindow.CenterOnScreen()
	myWindow.ShowAndRun()
}
